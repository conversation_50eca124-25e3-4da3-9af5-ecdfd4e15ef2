#!/usr/bin/env python3
"""
Demo script for pkl2.py - PKL to Video conversion
"""

import os
import subprocess
import sys

def run_demo():
    """Run demonstration of pkl2.py functionality"""
    
    print("🎬 PKL to Video Conversion Demo")
    print("=" * 50)
    
    # Example directory (adjust this path as needed)
    example_dir = "/home/<USER>/data/episode_demo/gpt_franka2_pick_up_place_apple/0609_165039/"
    
    if not os.path.exists(example_dir):
        print(f"❌ Example directory not found: {example_dir}")
        print("Please update the example_dir path in this script.")
        return
    
    print(f"📁 Using example directory: {example_dir}")
    print()
    
    # Demo 1: Single file processing
    print("🔍 Demo 1: Single PKL file processing")
    print("-" * 40)
    
    # Find a sample pkl file
    pkl_files = [f for f in os.listdir(example_dir) if f.endswith('.pkl')]
    if pkl_files:
        sample_file = os.path.join(example_dir, pkl_files[0])
        print(f"Processing: {pkl_files[0]}")
        
        result = subprocess.run([
            sys.executable, "data_scape/pkl2.py", sample_file
        ], capture_output=True, text=True)
        
        print("Output:")
        print(result.stdout)
        if result.stderr:
            print("Errors:", result.stderr)
    
    print("\n" + "=" * 50)
    
    # Demo 2: Separate camera videos (recommended)
    print("🎥 Demo 2: Generate separate camera videos (RECOMMENDED)")
    print("-" * 60)
    
    output_dir = "demo_camera_videos"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    print(f"Generating separate videos for each camera...")
    print(f"Output directory: {output_dir}")
    
    result = subprocess.run([
        sys.executable, "data_scape/pkl2.py", 
        example_dir, output_dir, "15"
    ], capture_output=True, text=True)
    
    print("Output:")
    print(result.stdout)
    if result.stderr:
        print("Errors:", result.stderr)
    
    # Show generated files
    if os.path.exists(output_dir):
        video_files = [f for f in os.listdir(output_dir) if f.endswith('.mp4')]
        if video_files:
            print(f"\n✅ Generated {len(video_files)} camera videos:")
            for video_file in sorted(video_files):
                file_path = os.path.join(output_dir, video_file)
                file_size = os.path.getsize(file_path)
                print(f"  📹 {video_file}: {file_size:,} bytes")
    
    print("\n" + "=" * 50)
    
    # Demo 3: Grid video (traditional mode)
    print("🎬 Demo 3: Generate grid video (traditional mode)")
    print("-" * 50)
    
    grid_video = "demo_grid_video.mp4"
    print(f"Generating grid video: {grid_video}")
    
    result = subprocess.run([
        sys.executable, "data_scape/pkl2.py", 
        example_dir, "--grid", grid_video, "15", "2", "2"
    ], capture_output=True, text=True)
    
    print("Output:")
    print(result.stdout)
    if result.stderr:
        print("Errors:", result.stderr)
    
    # Show generated file
    if os.path.exists(grid_video):
        file_size = os.path.getsize(grid_video)
        print(f"\n✅ Generated grid video:")
        print(f"  📹 {grid_video}: {file_size:,} bytes")
    
    print("\n" + "=" * 50)
    print("🎉 Demo completed!")
    print("\nGenerated files:")
    
    # List all generated files
    all_files = []
    
    # Camera videos
    if os.path.exists(output_dir):
        for f in os.listdir(output_dir):
            if f.endswith('.mp4'):
                all_files.append(os.path.join(output_dir, f))
    
    # Grid video
    if os.path.exists(grid_video):
        all_files.append(grid_video)
    
    # Preview image
    preview_files = [f for f in os.listdir('.') if f.endswith('_preview.jpg')]
    all_files.extend(preview_files)
    
    for file_path in sorted(all_files):
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            print(f"  📄 {file_path}: {file_size:,} bytes")
    
    print(f"\n📖 For more information, see: PKL_TO_VIDEO_README.md")

if __name__ == "__main__":
    run_demo()
