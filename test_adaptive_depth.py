#!/usr/bin/env python3
"""
Test script for adaptive depth colormap functionality
"""

import os
import subprocess
import sys

def test_adaptive_depth():
    """Test the adaptive depth colormap functionality"""
    
    print("🎨 Adaptive Depth Colormap Test")
    print("=" * 60)
    
    # Test dataset directory
    dataset_dir = "/home/<USER>/data/episode_demo/gpt_franka_fr3_dualArm-gripper-6cameras_4_transfer_cubes_with_obstacle_250603-2"
    
    if not os.path.exists(dataset_dir):
        print(f"❌ Dataset directory not found: {dataset_dir}")
        return
    
    # Find a sample directory
    sample_dirs = [d for d in os.listdir(dataset_dir) 
                   if os.path.isdir(os.path.join(dataset_dir, d))]
    
    if not sample_dirs:
        print("❌ No sample directories found")
        return
    
    sample_dir = os.path.join(dataset_dir, sample_dirs[0])
    print(f"📁 Using sample directory: {sample_dirs[0]}")
    print()
    
    # Test 1: Adaptive depth mapping (default)
    print("🔍 Test 1: Adaptive depth mapping (per-frame optimization)")
    print("-" * 60)
    
    output_dir = "test_adaptive_depth"
    print(f"Extracting with adaptive depth mapping to: {output_dir}")
    
    result = subprocess.run([
        sys.executable, "data_scape/pkl2.py", 
        sample_dir, "--extract", output_dir, "true"
    ], capture_output=True, text=True)
    
    print("Output:")
    print(result.stdout)
    if result.stderr:
        print("Errors:", result.stderr)
    
    # Check generated files
    if os.path.exists(output_dir):
        print(f"\n✅ Adaptive depth extraction completed!")
        
        # Check depth images
        depth_images_dir = os.path.join(output_dir, "depth_images")
        if os.path.exists(depth_images_dir):
            depth_files = [f for f in os.listdir(depth_images_dir) if f.endswith('_d.png')]
            print(f"📷 Found {len(depth_files)} adaptive depth images")
            
            # Show examples from different cameras
            cameras = set()
            for depth_file in depth_files[:10]:
                parts = depth_file.split('_')
                if len(parts) >= 3:
                    camera = parts[-2]  # Extract camera name
                    cameras.add(camera)
            
            print(f"📷 Cameras with adaptive depth: {sorted(cameras)}")
            
            # Show file sizes for comparison
            for camera in sorted(cameras):
                camera_files = [f for f in depth_files if f'_{camera}_d.png' in f]
                if camera_files:
                    sample_file = camera_files[0]
                    file_path = os.path.join(depth_images_dir, sample_file)
                    file_size = os.path.getsize(file_path)
                    print(f"   {camera}: {sample_file} ({file_size:,} bytes)")
        
        # Check depth videos
        depth_videos_dir = os.path.join(output_dir, "depth_videos")
        if os.path.exists(depth_videos_dir):
            depth_videos = [f for f in os.listdir(depth_videos_dir) if f.endswith('_d.mp4')]
            print(f"\n🎬 Found {len(depth_videos)} adaptive depth videos")
            
            for depth_video in depth_videos:
                video_path = os.path.join(depth_videos_dir, depth_video)
                file_size = os.path.getsize(video_path)
                print(f"   {depth_video}: {file_size:,} bytes")
    
    print("\n" + "=" * 60)
    
    # Test 2: Test adaptive depth function directly
    print("🔍 Test 2: Direct adaptive depth function test")
    print("-" * 60)
    
    try:
        # Import the functions
        sys.path.append('data_scape')
        from pkl2 import apply_depth_colormap, read_pkl, decode_image
        import numpy as np
        import cv2
        
        # Load a sample pkl file
        pkl_files = [f for f in os.listdir(sample_dir) if f.endswith('.pkl')]
        if pkl_files:
            sample_pkl = os.path.join(sample_dir, pkl_files[0])
            print(f"Testing with: {pkl_files[0]}")
            
            data = read_pkl(sample_pkl)
            if data and 'depths' in data:
                print("Testing adaptive depth mapping for each camera:")
                
                for camera_name, depth_data in data['depths'].items():
                    decoded_depth = decode_image(depth_data)
                    if decoded_depth is not None:
                        # Convert to grayscale if needed
                        if len(decoded_depth.shape) == 3:
                            depth_gray = cv2.cvtColor(decoded_depth, cv2.COLOR_BGR2GRAY)
                        else:
                            depth_gray = decoded_depth
                        
                        # Get depth statistics
                        valid_depths = depth_gray[depth_gray > 0]
                        if len(valid_depths) > 0:
                            min_depth = np.min(valid_depths)
                            max_depth = np.max(valid_depths)
                            p1 = np.percentile(valid_depths, 1)
                            p99 = np.percentile(valid_depths, 99)
                            
                            print(f"  📷 {camera_name}:")
                            print(f"    Raw range: {min_depth:.0f} - {max_depth:.0f}")
                            print(f"    Adaptive range (1%-99%): {p1:.0f} - {p99:.0f}")
                            
                            # Test adaptive colormap
                            colored_adaptive = apply_depth_colormap(depth_gray, None, 'jet')
                            print(f"    Adaptive result: {colored_adaptive.shape}")
                            
                            # Test fixed range colormap
                            colored_fixed = apply_depth_colormap(depth_gray, (0, 255), 'jet')
                            print(f"    Fixed range result: {colored_fixed.shape}")
                        else:
                            print(f"  📷 {camera_name}: No valid depth data")
                
                print("✅ Adaptive depth function working correctly")
            else:
                print("❌ No depth data found in sample file")
        else:
            print("❌ No pkl files found")
        
    except ImportError as e:
        print(f"❌ Could not import functions: {e}")
    except Exception as e:
        print(f"❌ Error testing functions: {e}")
    
    print("\n" + "=" * 60)
    
    # Test 3: Compare different cameras' depth ranges
    print("🔍 Test 3: Camera depth range comparison")
    print("-" * 60)
    
    if os.path.exists(output_dir):
        depth_images_dir = os.path.join(output_dir, "depth_images")
        if os.path.exists(depth_images_dir):
            print("Analyzing adaptive depth images by camera:")
            
            # Group files by camera
            camera_files = {}
            depth_files = [f for f in os.listdir(depth_images_dir) if f.endswith('_d.png')]
            
            for depth_file in depth_files:
                parts = depth_file.split('_')
                if len(parts) >= 3:
                    camera = parts[-2]  # Extract camera name
                    if camera not in camera_files:
                        camera_files[camera] = []
                    camera_files[camera].append(depth_file)
            
            # Show statistics for each camera
            for camera, files in camera_files.items():
                print(f"  📷 {camera}: {len(files)} images")
                
                # Sample a few files to check size variation
                sample_files = files[:3]
                sizes = []
                for file in sample_files:
                    file_path = os.path.join(depth_images_dir, file)
                    file_size = os.path.getsize(file_path)
                    sizes.append(file_size)
                
                if sizes:
                    avg_size = sum(sizes) / len(sizes)
                    print(f"    Average size: {avg_size:,.0f} bytes")
                    print(f"    Size range: {min(sizes):,} - {max(sizes):,} bytes")
    
    print("\n" + "=" * 60)
    print("🎉 Adaptive depth colormap test completed!")
    
    print("\nKey advantages of adaptive depth mapping:")
    print("✅ Each camera gets optimal contrast based on its depth range")
    print("✅ No need for unified depth standards")
    print("✅ Better visualization of depth differences per camera")
    print("✅ Automatic adaptation to different scenes and camera positions")
    
    print("\nGenerated files:")
    test_dirs = ["test_adaptive_depth"]
    for test_dir in test_dirs:
        if os.path.exists(test_dir):
            print(f"  📁 {test_dir}/")
            subdirs = [d for d in os.listdir(test_dir) if os.path.isdir(os.path.join(test_dir, d))]
            for subdir in subdirs:
                subdir_path = os.path.join(test_dir, subdir)
                file_count = len([f for f in os.listdir(subdir_path) if os.path.isfile(os.path.join(subdir_path, f))])
                print(f"    📁 {subdir}/: {file_count} files")

if __name__ == "__main__":
    test_adaptive_depth()
