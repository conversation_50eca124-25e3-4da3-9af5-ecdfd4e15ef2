#!/usr/bin/env python3
"""
Test script for depth colormap functionality
"""

import os
import subprocess
import sys

def test_depth_colormap():
    """Test the depth colormap functionality"""
    
    print("🎨 Depth Colormap Test")
    print("=" * 60)
    
    # Test dataset directory
    dataset_dir = "/home/<USER>/data/episode_demo/gpt_franka_fr3_dualArm-gripper-6cameras_4_transfer_cubes_with_obstacle_250603-2"
    
    if not os.path.exists(dataset_dir):
        print(f"❌ Dataset directory not found: {dataset_dir}")
        return
    
    # Find a sample directory
    sample_dirs = [d for d in os.listdir(dataset_dir) 
                   if os.path.isdir(os.path.join(dataset_dir, d))]
    
    if not sample_dirs:
        print("❌ No sample directories found")
        return
    
    sample_dir = os.path.join(dataset_dir, sample_dirs[0])
    print(f"📁 Using sample directory: {sample_dirs[0]}")
    print()
    
    # Test 1: Extract with default colormap (jet)
    print("🔍 Test 1: Default colormap (jet)")
    print("-" * 60)
    
    output_dir = "test_depth_jet"
    print(f"Extracting with jet colormap to: {output_dir}")
    
    result = subprocess.run([
        sys.executable, "data_scape/pkl2.py", 
        sample_dir, "--extract", output_dir, "true"
    ], capture_output=True, text=True)
    
    print("Output:")
    print(result.stdout)
    if result.stderr:
        print("Errors:", result.stderr)
    
    # Check for depth colormap standard
    colormap_file = os.path.join(output_dir, "depth_color.png")
    if os.path.exists(colormap_file):
        print(f"✅ Depth colormap standard created: {colormap_file}")
        file_size = os.path.getsize(colormap_file)
        print(f"   File size: {file_size:,} bytes")
    else:
        print(f"❌ Depth colormap standard not found: {colormap_file}")
    
    # Check depth images
    depth_images_dir = os.path.join(output_dir, "depth_images")
    if os.path.exists(depth_images_dir):
        depth_files = [f for f in os.listdir(depth_images_dir) if f.endswith('_d.png')]
        print(f"✅ Found {len(depth_files)} colored depth images")
        
        # Show some examples
        for i, depth_file in enumerate(depth_files[:3]):
            depth_path = os.path.join(depth_images_dir, depth_file)
            file_size = os.path.getsize(depth_path)
            print(f"   {depth_file}: {file_size:,} bytes")
    
    # Check depth videos
    depth_videos_dir = os.path.join(output_dir, "depth_videos")
    if os.path.exists(depth_videos_dir):
        depth_videos = [f for f in os.listdir(depth_videos_dir) if f.endswith('_d.mp4')]
        print(f"✅ Found {len(depth_videos)} colored depth videos")
        
        for depth_video in depth_videos:
            video_path = os.path.join(depth_videos_dir, depth_video)
            file_size = os.path.getsize(video_path)
            print(f"   {depth_video}: {file_size:,} bytes")
    
    print("\n" + "=" * 60)
    
    # Test 2: Test different colormap (viridis)
    print("🔍 Test 2: Different colormap (viridis)")
    print("-" * 60)
    
    # First, let's create a simple test to verify colormap functionality
    print("Testing colormap functions directly...")
    
    try:
        # Import the functions
        sys.path.append('data_scape')
        from pkl2 import create_depth_colormap_standard, apply_depth_colormap, analyze_depth_range
        import numpy as np
        import cv2
        
        # Test colormap standard creation
        test_colormap_path = "test_viridis_colormap.png"
        print(f"Creating viridis colormap standard: {test_colormap_path}")
        
        try:
            create_depth_colormap_standard(test_colormap_path, (0, 3000), 'viridis')
            if os.path.exists(test_colormap_path):
                print(f"✅ Viridis colormap created successfully")
            else:
                print(f"❌ Viridis colormap creation failed")
        except Exception as e:
            print(f"⚠️  Matplotlib not available, using OpenCV fallback: {e}")
        
        # Test colormap application
        print("Testing colormap application...")
        
        # Create a test depth image
        test_depth = np.random.randint(0, 3000, (100, 100), dtype=np.uint8)
        
        # Apply different colormaps
        colormaps = ['jet', 'viridis', 'plasma', 'hot', 'cool']
        for cmap in colormaps:
            try:
                colored = apply_depth_colormap(test_depth, (0, 3000), cmap)
                print(f"✅ {cmap} colormap applied successfully: {colored.shape}")
            except Exception as e:
                print(f"❌ {cmap} colormap failed: {e}")
        
        print("✅ Colormap functions working correctly")
        
    except ImportError as e:
        print(f"❌ Could not import functions: {e}")
    except Exception as e:
        print(f"❌ Error testing functions: {e}")
    
    print("\n" + "=" * 60)
    
    # Test 3: Check file structure and naming
    print("🔍 Test 3: File structure verification")
    print("-" * 60)
    
    if os.path.exists(output_dir):
        print(f"📁 Output directory structure for {output_dir}:")
        
        # Check all subdirectories
        subdirs = ['rgb_images', 'depth_images', 'rgb_videos', 'depth_videos']
        for subdir in subdirs:
            subdir_path = os.path.join(output_dir, subdir)
            if os.path.exists(subdir_path):
                files = os.listdir(subdir_path)
                print(f"  📁 {subdir}/: {len(files)} files")
                
                # Show file examples
                for file in files[:2]:
                    file_path = os.path.join(subdir_path, file)
                    file_size = os.path.getsize(file_path)
                    print(f"    📄 {file}: {file_size:,} bytes")
            else:
                print(f"  ❌ {subdir}/: Not found")
        
        # Check for depth colormap standard
        if os.path.exists(colormap_file):
            print(f"  🎨 depth_color.png: Standard colormap reference")
    
    print("\n" + "=" * 60)
    print("🎉 Depth colormap test completed!")
    
    print("\nGenerated files:")
    test_dirs = ["test_depth_jet"]
    for test_dir in test_dirs:
        if os.path.exists(test_dir):
            print(f"  📁 {test_dir}/")
            subdirs = [d for d in os.listdir(test_dir) if os.path.isdir(os.path.join(test_dir, d))]
            for subdir in subdirs:
                subdir_path = os.path.join(test_dir, subdir)
                file_count = len([f for f in os.listdir(subdir_path) if os.path.isfile(os.path.join(subdir_path, f))])
                print(f"    📁 {subdir}/: {file_count} files")
            
            # Check for colormap standard
            colormap_path = os.path.join(test_dir, "depth_color.png")
            if os.path.exists(colormap_path):
                print(f"    🎨 depth_color.png: Colormap standard")
    
    # Check for standalone test files
    test_files = ["test_viridis_colormap.png"]
    for test_file in test_files:
        if os.path.exists(test_file):
            file_size = os.path.getsize(test_file)
            print(f"  🎨 {test_file}: {file_size:,} bytes")

if __name__ == "__main__":
    test_depth_colormap()
