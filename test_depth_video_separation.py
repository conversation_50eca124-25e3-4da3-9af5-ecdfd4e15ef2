#!/usr/bin/env python3
"""
Test script for depth video separation functionality
"""

import os
import subprocess
import sys

def test_depth_video_separation():
    """Test the depth video separation functionality"""
    
    print("🎬 Depth Video Separation Test")
    print("=" * 60)
    
    # Test dataset directory
    dataset_dir = "/home/<USER>/data/episode_demo/gpt_franka_fr3_dualArm-gripper-6cameras_4_transfer_cubes_with_obstacle_250603-2"
    
    if not os.path.exists(dataset_dir):
        print(f"❌ Dataset directory not found: {dataset_dir}")
        return
    
    # Find a sample directory (exclude generated directories)
    sample_dirs = [d for d in os.listdir(dataset_dir) 
                   if os.path.isdir(os.path.join(dataset_dir, d)) and 
                   d not in ['rgb_images', 'depth_images', 'rgb_videos', 'depth_videos'] and
                   d.startswith('0606_')]
    
    if not sample_dirs:
        print("❌ No sample directories found")
        return
    
    # Use a different sample to avoid conflicts
    sample_dir = os.path.join(dataset_dir, sample_dirs[1] if len(sample_dirs) > 1 else sample_dirs[0])
    print(f"📁 Using sample directory: {os.path.basename(sample_dir)}")
    print(f"📁 Full sample path: {sample_dir}")
    print()
    
    # Test 1: Extract with depth video separation
    print("🔍 Test 1: Depth video separation (raw + color)")
    print("-" * 60)
    
    print(f"Extracting with separated depth videos")
    
    result = subprocess.run([
        sys.executable, "data_scape/pkl2.py", 
        sample_dir, "--extract", "auto", "false"  # Don't save individual images for speed
    ], capture_output=True, text=True)
    
    print("Output:")
    print(result.stdout)
    if result.stderr:
        print("Errors:", result.stderr)
    
    # Check if depth video directories were created
    depth_videos_dir = os.path.join(sample_dir, "depth_videos")
    raw_depth_videos_dir = os.path.join(depth_videos_dir, "raw_depth_videos")
    color_depth_videos_dir = os.path.join(depth_videos_dir, "color_depth_videos")
    
    print(f"\n✅ Checking depth video structure in: {sample_dir}")
    
    # Check main depth_videos directory
    if os.path.exists(depth_videos_dir):
        print(f"  📁 depth_videos/: exists")
        
        # List contents of depth_videos directory
        depth_contents = os.listdir(depth_videos_dir)
        for item in depth_contents:
            item_path = os.path.join(depth_videos_dir, item)
            if os.path.isdir(item_path):
                file_count = len([f for f in os.listdir(item_path) if os.path.isfile(os.path.join(item_path, f))])
                print(f"    📁 {item}/: {file_count} files")
            else:
                file_size = os.path.getsize(item_path)
                print(f"    📄 {item}: {file_size:,} bytes")
    else:
        print(f"  ❌ depth_videos/: Not found")
    
    # Check raw depth videos
    if os.path.exists(raw_depth_videos_dir):
        raw_videos = [f for f in os.listdir(raw_depth_videos_dir) if f.endswith('.mp4')]
        print(f"\n  🔍 raw_depth_videos/: {len(raw_videos)} files")
        
        for video in raw_videos:
            video_path = os.path.join(raw_depth_videos_dir, video)
            file_size = os.path.getsize(video_path)
            print(f"    📄 {video}: {file_size:,} bytes")
    else:
        print(f"\n  ❌ raw_depth_videos/: Not found")
    
    # Check color depth videos
    if os.path.exists(color_depth_videos_dir):
        color_videos = [f for f in os.listdir(color_depth_videos_dir) if f.endswith('.mp4')]
        print(f"\n  🎨 color_depth_videos/: {len(color_videos)} files")
        
        for video in color_videos:
            video_path = os.path.join(color_depth_videos_dir, video)
            file_size = os.path.getsize(video_path)
            print(f"    📄 {video}: {file_size:,} bytes")
    else:
        print(f"\n  ❌ color_depth_videos/: Not found")
    
    print("\n" + "=" * 60)
    
    # Test 2: Directory structure verification
    print("🔍 Test 2: Complete directory structure verification")
    print("-" * 60)
    
    print(f"Sample directory structure:")
    print(f"📁 {os.path.basename(sample_dir)}/")
    
    # List all items in sample directory
    items = os.listdir(sample_dir)
    items.sort()
    
    expected_dirs = ["rgb_images", "depth_images", "rgb_videos", "depth_videos"]
    pkl_count = 0
    
    for item in items:
        item_path = os.path.join(sample_dir, item)
        if os.path.isdir(item_path):
            if item in expected_dirs:
                if item == "depth_videos":
                    # Special handling for depth_videos
                    subdirs = [d for d in os.listdir(item_path) if os.path.isdir(os.path.join(item_path, d))]
                    print(f"  📁 {item}/: {len(subdirs)} subdirectories")
                    for subdir in subdirs:
                        subdir_path = os.path.join(item_path, subdir)
                        file_count = len([f for f in os.listdir(subdir_path) if os.path.isfile(os.path.join(subdir_path, f))])
                        print(f"    📁 {subdir}/: {file_count} files")
                else:
                    file_count = len([f for f in os.listdir(item_path) if os.path.isfile(os.path.join(item_path, f))])
                    print(f"  📁 {item}/: {file_count} files (🎬 Generated)")
            else:
                print(f"  📁 {item}/: directory")
        else:
            if item.endswith('.pkl'):
                pkl_count += 1
            else:
                file_size = os.path.getsize(item_path)
                print(f"  📄 {item}: {file_size:,} bytes")
    
    print(f"  📊 Total pkl files: {pkl_count}")
    
    print("\n" + "=" * 60)
    
    # Test 3: File naming convention verification
    print("🔍 Test 3: Depth video naming convention verification")
    print("-" * 60)
    
    if os.path.exists(raw_depth_videos_dir):
        raw_videos = [f for f in os.listdir(raw_depth_videos_dir) if f.endswith('.mp4')]
        print("🔍 Raw depth video naming examples:")
        for video in raw_videos[:3]:
            print(f"  {video}")
    
    if os.path.exists(color_depth_videos_dir):
        color_videos = [f for f in os.listdir(color_depth_videos_dir) if f.endswith('.mp4')]
        print("\n🎨 Color depth video naming examples:")
        for video in color_videos[:3]:
            print(f"  {video}")
    
    print("\n" + "=" * 60)
    
    # Test 4: Size comparison
    print("🔍 Test 4: File size comparison (raw vs color depth)")
    print("-" * 60)
    
    if os.path.exists(raw_depth_videos_dir) and os.path.exists(color_depth_videos_dir):
        raw_videos = [f for f in os.listdir(raw_depth_videos_dir) if f.endswith('.mp4')]
        color_videos = [f for f in os.listdir(color_depth_videos_dir) if f.endswith('.mp4')]
        
        print("Size comparison by camera:")
        
        # Extract camera names
        cameras = set()
        for video in raw_videos:
            if '_raw_d.mp4' in video:
                camera = video.replace('_raw_d.mp4', '')
                cameras.add(camera)
        
        for camera in sorted(cameras):
            raw_file = f"{camera}_raw_d.mp4"
            color_file = f"{camera}_color_d.mp4"
            
            raw_path = os.path.join(raw_depth_videos_dir, raw_file)
            color_path = os.path.join(color_depth_videos_dir, color_file)
            
            if os.path.exists(raw_path) and os.path.exists(color_path):
                raw_size = os.path.getsize(raw_path)
                color_size = os.path.getsize(color_path)
                ratio = color_size / raw_size if raw_size > 0 else 0
                
                print(f"  📷 {camera}:")
                print(f"    🔍 Raw: {raw_size:,} bytes")
                print(f"    🎨 Color: {color_size:,} bytes")
                print(f"    📊 Ratio: {ratio:.2f}x")
    
    print("\n" + "=" * 60)
    print("🎉 Depth video separation test completed!")
    
    print("\nKey advantages of depth video separation:")
    print("✅ Raw depth videos: Original grayscale depth information")
    print("✅ Color depth videos: Enhanced visualization with color mapping")
    print("✅ Clear organization: Separate directories for different purposes")
    print("✅ Flexible usage: Choose appropriate format for analysis or presentation")
    
    print("\nGenerated structure:")
    print("sample_directory/")
    print("├── *.pkl                    # Original pkl files")
    print("├── rgb_images/              # RGB PNG images")
    print("├── depth_images/            # Color depth PNG images")
    print("├── rgb_videos/              # RGB videos")
    print("└── depth_videos/")
    print("    ├── raw_depth_videos/    # 🔍 Raw grayscale depth videos")
    print("    └── color_depth_videos/  # 🎨 Color-mapped depth videos")
    
    print("\nUsage examples:")
    print("# Extract with separated depth videos")
    print("python pkl2.py /path/to/sample --extract auto true")

if __name__ == "__main__":
    test_depth_video_separation()
