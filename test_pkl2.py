#!/usr/bin/env python3
"""
Test script for pkl2.py functionality
"""

import os
import sys
import subprocess

def test_pkl2_functionality():
    """Test the pkl2.py script functionality"""
    
    print("Testing pkl2.py functionality...")
    
    # Test 1: Show help message
    print("\n1. Testing help message:")
    result = subprocess.run([sys.executable, "data_scape/pkl2.py"], 
                          capture_output=True, text=True)
    print("STDOUT:", result.stdout)
    if result.stderr:
        print("STDERR:", result.stderr)
    
    # Test 2: Test with example pkl file path (if it exists)
    example_pkl_path = "/home/<USER>/data/episode_demo/gpt_franka2_pick_up_place_apple/0609_165039/2025-06-09T16_50_53.997363.pkl"
    
    if os.path.exists(example_pkl_path):
        print(f"\n2. Testing single pkl file: {example_pkl_path}")
        result = subprocess.run([sys.executable, "data_scape/pkl2.py", example_pkl_path], 
                              capture_output=True, text=True)
        print("STDOUT:", result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
    else:
        print(f"\n2. Example pkl file not found: {example_pkl_path}")
    
    # Test 3: Test separate camera videos (new default behavior)
    example_dir = "/home/<USER>/data/episode_demo/gpt_franka2_pick_up_place_apple/0609_165039/"

    if os.path.exists(example_dir):
        print(f"\n3. Testing separate camera videos: {example_dir}")

        # Create test output directory
        test_output_dir = "test_camera_videos"
        if not os.path.exists(test_output_dir):
            os.makedirs(test_output_dir)

        result = subprocess.run([sys.executable, "data_scape/pkl2.py", example_dir, test_output_dir, "10"],
                              capture_output=True, text=True)
        print("STDOUT:", result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)

        # Check if camera videos were created
        camera_videos = []
        if os.path.exists(test_output_dir):
            for file in os.listdir(test_output_dir):
                if file.endswith('.mp4'):
                    camera_videos.append(file)
                    file_size = os.path.getsize(os.path.join(test_output_dir, file))
                    print(f"✓ Created {file}: {file_size} bytes")

        if camera_videos:
            print(f"✓ Successfully created {len(camera_videos)} camera videos: {camera_videos}")
        else:
            print("✗ No camera videos were created")

    # Test 4: Test grid video (legacy mode)
    if os.path.exists(example_dir):
        print(f"\n4. Testing grid video mode: {example_dir}")
        result = subprocess.run([sys.executable, "data_scape/pkl2.py", example_dir, "--grid", "test_grid.mp4", "10", "2", "2"],
                              capture_output=True, text=True)
        print("STDOUT:", result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)

        # Check if grid video was created
        if os.path.exists("test_grid.mp4"):
            print("✓ Grid video file created successfully!")
            file_size = os.path.getsize("test_grid.mp4")
            print(f"  Grid video file size: {file_size} bytes")
        else:
            print("✗ Grid video file was not created")
    else:
        print(f"\n3-4. Example directory not found: {example_dir}")
    
    print("\nTest completed!")

if __name__ == "__main__":
    test_pkl2_functionality()
