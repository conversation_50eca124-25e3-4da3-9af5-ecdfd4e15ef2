#!/usr/bin/env python3
"""
Test script for auto FPS calculation functionality
"""

import os
import subprocess
import sys

def test_auto_fps():
    """Test the auto FPS calculation functionality"""
    
    print("🎬 Auto FPS Calculation Test")
    print("=" * 50)
    
    # Test dataset directory
    dataset_dir = "/home/<USER>/data/episode_demo/gpt_franka_fr3_dualArm-gripper-6cameras_4_transfer_cubes_with_obstacle_250603-2"
    
    if not os.path.exists(dataset_dir):
        print(f"❌ Dataset directory not found: {dataset_dir}")
        return
    
    # Find a sample directory
    sample_dirs = [d for d in os.listdir(dataset_dir) 
                   if os.path.isdir(os.path.join(dataset_dir, d))]
    
    if not sample_dirs:
        print("❌ No sample directories found")
        return
    
    sample_dir = os.path.join(dataset_dir, sample_dirs[0])
    print(f"📁 Using sample directory: {sample_dirs[0]}")
    print()
    
    # Test 1: Auto FPS calculation for single sample
    print("🔍 Test 1: Auto FPS calculation for single sample")
    print("-" * 50)
    
    output_dir = "test_auto_fps_single"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    print("Running with auto FPS calculation...")
    result = subprocess.run([
        sys.executable, "data_scape/pkl2.py", 
        sample_dir, output_dir, "auto"
    ], capture_output=True, text=True)
    
    print("Output:")
    print(result.stdout)
    if result.stderr:
        print("Errors:", result.stderr)
    
    # Check generated files
    if os.path.exists(output_dir):
        video_files = [f for f in os.listdir(output_dir) if f.endswith('.mp4')]
        if video_files:
            print(f"\n✅ Generated {len(video_files)} videos with auto FPS:")
            for video_file in sorted(video_files):
                file_path = os.path.join(output_dir, video_file)
                file_size = os.path.getsize(file_path)
                print(f"  📹 {video_file}: {file_size:,} bytes")
    
    print("\n" + "=" * 50)
    
    # Test 2: Compare with manual FPS
    print("🔍 Test 2: Compare auto FPS vs manual FPS")
    print("-" * 50)
    
    output_dir_manual = "test_manual_fps_single"
    if not os.path.exists(output_dir_manual):
        os.makedirs(output_dir_manual)
    
    print("Running with manual FPS (15)...")
    result = subprocess.run([
        sys.executable, "data_scape/pkl2.py", 
        sample_dir, output_dir_manual, "15"
    ], capture_output=True, text=True)
    
    print("Output:")
    print(result.stdout)
    if result.stderr:
        print("Errors:", result.stderr)
    
    # Compare file sizes
    if os.path.exists(output_dir_manual):
        video_files_manual = [f for f in os.listdir(output_dir_manual) if f.endswith('.mp4')]
        if video_files_manual:
            print(f"\n✅ Generated {len(video_files_manual)} videos with manual FPS (15):")
            for video_file in sorted(video_files_manual):
                file_path = os.path.join(output_dir_manual, video_file)
                file_size = os.path.getsize(file_path)
                print(f"  📹 {video_file}: {file_size:,} bytes")
    
    print("\n" + "=" * 50)
    
    # Test 3: Dataset processing with auto FPS
    print("🔍 Test 3: Dataset processing with auto FPS")
    print("-" * 50)
    
    dataset_output_dir = "test_auto_fps_dataset"
    print("Running dataset processing with auto FPS...")
    
    result = subprocess.run([
        sys.executable, "data_scape/pkl2.py", 
        dataset_dir, "--dataset", dataset_output_dir, "auto"
    ], capture_output=True, text=True)
    
    print("Output:")
    print(result.stdout)
    if result.stderr:
        print("Errors:", result.stderr)
    
    # Show results
    if os.path.exists(dataset_output_dir):
        sample_dirs_output = [d for d in os.listdir(dataset_output_dir) 
                             if os.path.isdir(os.path.join(dataset_output_dir, d))]
        
        if sample_dirs_output:
            print(f"\n✅ Generated videos for {len(sample_dirs_output)} samples with auto FPS:")
            total_videos = 0
            total_size = 0
            
            for sample_name in sorted(sample_dirs_output):
                sample_path = os.path.join(dataset_output_dir, sample_name)
                video_files = [f for f in os.listdir(sample_path) if f.endswith('.mp4')]
                
                if video_files:
                    sample_size = sum(os.path.getsize(os.path.join(sample_path, f)) 
                                    for f in video_files)
                    total_videos += len(video_files)
                    total_size += sample_size
                    
                    print(f"  📁 {sample_name}: {len(video_files)} videos ({sample_size:,} bytes)")
            
            print(f"\n📊 Total: {total_videos} videos, {total_size:,} bytes")
    
    print("\n" + "=" * 50)
    
    # Test 4: Grid video with auto FPS
    print("🔍 Test 4: Grid video with auto FPS")
    print("-" * 50)
    
    grid_video = "test_auto_fps_grid.mp4"
    print("Running grid video generation with auto FPS...")
    
    result = subprocess.run([
        sys.executable, "data_scape/pkl2.py", 
        sample_dir, "--grid", grid_video, "auto", "2", "2"
    ], capture_output=True, text=True)
    
    print("Output:")
    print(result.stdout)
    if result.stderr:
        print("Errors:", result.stderr)
    
    # Check generated file
    if os.path.exists(grid_video):
        file_size = os.path.getsize(grid_video)
        print(f"\n✅ Generated grid video with auto FPS:")
        print(f"  📹 {grid_video}: {file_size:,} bytes")
    
    print("\n" + "=" * 50)
    print("🎉 Auto FPS calculation test completed!")
    
    print("\nGenerated directories and files:")
    for test_item in ["test_auto_fps_single", "test_manual_fps_single", 
                      "test_auto_fps_dataset", "test_auto_fps_grid.mp4"]:
        if os.path.exists(test_item):
            if os.path.isdir(test_item):
                print(f"  📁 {test_item}/")
            else:
                print(f"  📄 {test_item}")

if __name__ == "__main__":
    test_auto_fps()
