#!/usr/bin/env python3
"""
Test script for image and depth extraction functionality
"""

import os
import subprocess
import sys

def test_image_depth_extraction():
    """Test the image and depth extraction functionality"""
    
    print("🖼️ Image and Depth Extraction Test")
    print("=" * 60)
    
    # Test dataset directory
    dataset_dir = "/home/<USER>/data/episode_demo/gpt_franka_fr3_dualArm-gripper-6cameras_4_transfer_cubes_with_obstacle_250603-2"
    
    if not os.path.exists(dataset_dir):
        print(f"❌ Dataset directory not found: {dataset_dir}")
        return
    
    # Find a sample directory
    sample_dirs = [d for d in os.listdir(dataset_dir) 
                   if os.path.isdir(os.path.join(dataset_dir, d))]
    
    if not sample_dirs:
        print("❌ No sample directories found")
        return
    
    sample_dir = os.path.join(dataset_dir, sample_dirs[0])
    print(f"📁 Using sample directory: {sample_dirs[0]}")
    print()
    
    # Test 1: Single sample image/depth extraction
    print("🔍 Test 1: Single sample image/depth extraction")
    print("-" * 60)
    
    output_dir = "test_extracted_single"
    print(f"Extracting images and depths to: {output_dir}")
    
    result = subprocess.run([
        sys.executable, "data_scape/pkl2.py", 
        sample_dir, "--extract", output_dir, "true"
    ], capture_output=True, text=True)
    
    print("Output:")
    print(result.stdout)
    if result.stderr:
        print("Errors:", result.stderr)
    
    # Check generated structure
    if os.path.exists(output_dir):
        print(f"\n✅ Extraction completed! Checking structure:")
        
        # Check directories
        expected_dirs = ["rgb_images", "depth_images", "rgb_videos", "depth_videos"]
        for dir_name in expected_dirs:
            dir_path = os.path.join(output_dir, dir_name)
            if os.path.exists(dir_path):
                file_count = len([f for f in os.listdir(dir_path) if os.path.isfile(os.path.join(dir_path, f))])
                print(f"  📁 {dir_name}: {file_count} files")
                
                # Show some example files
                files = os.listdir(dir_path)[:3]
                for file in files:
                    file_path = os.path.join(dir_path, file)
                    file_size = os.path.getsize(file_path)
                    print(f"    📄 {file}: {file_size:,} bytes")
            else:
                print(f"  ❌ {dir_name}: Not found")
    
    print("\n" + "=" * 60)
    
    # Test 2: Dataset image/depth extraction (limited to first sample)
    print("🔍 Test 2: Dataset image/depth extraction")
    print("-" * 60)
    
    dataset_output_dir = "test_extracted_dataset"
    print(f"Extracting dataset images and depths to: {dataset_output_dir}")
    
    # Only process first sample to save time
    first_sample_dir = os.path.join(dataset_dir, sample_dirs[0])
    
    result = subprocess.run([
        sys.executable, "data_scape/pkl2.py", 
        first_sample_dir, "--extract", dataset_output_dir, "false"  # Don't save individual images for speed
    ], capture_output=True, text=True)
    
    print("Output:")
    print(result.stdout)
    if result.stderr:
        print("Errors:", result.stderr)
    
    # Check generated structure
    if os.path.exists(dataset_output_dir):
        print(f"\n✅ Dataset extraction completed! Checking structure:")
        
        # Check directories
        expected_dirs = ["rgb_images", "depth_images", "rgb_videos", "depth_videos"]
        for dir_name in expected_dirs:
            dir_path = os.path.join(dataset_output_dir, dir_name)
            if os.path.exists(dir_path):
                file_count = len([f for f in os.listdir(dir_path) if os.path.isfile(os.path.join(dir_path, f))])
                print(f"  📁 {dir_name}: {file_count} files")
                
                # Show video files specifically
                if "videos" in dir_name:
                    video_files = [f for f in os.listdir(dir_path) if f.endswith('.mp4')]
                    for video_file in video_files:
                        video_path = os.path.join(dir_path, video_file)
                        video_size = os.path.getsize(video_path)
                        print(f"    🎬 {video_file}: {video_size:,} bytes")
            else:
                print(f"  ❌ {dir_name}: Not found")
    
    print("\n" + "=" * 60)
    
    # Test 3: Check file naming conventions
    print("🔍 Test 3: File naming convention verification")
    print("-" * 60)
    
    if os.path.exists(output_dir):
        rgb_images_dir = os.path.join(output_dir, "rgb_images")
        depth_images_dir = os.path.join(output_dir, "depth_images")
        
        if os.path.exists(rgb_images_dir):
            rgb_files = [f for f in os.listdir(rgb_images_dir) if f.endswith('.png')][:5]
            print("📷 RGB image naming examples:")
            for file in rgb_files:
                print(f"  {file}")
        
        if os.path.exists(depth_images_dir):
            depth_files = [f for f in os.listdir(depth_images_dir) if f.endswith('.png')][:5]
            print("\n🔍 Depth image naming examples:")
            for file in depth_files:
                print(f"  {file}")
        
        rgb_videos_dir = os.path.join(output_dir, "rgb_videos")
        depth_videos_dir = os.path.join(output_dir, "depth_videos")
        
        if os.path.exists(rgb_videos_dir):
            rgb_videos = [f for f in os.listdir(rgb_videos_dir) if f.endswith('.mp4')]
            print(f"\n🎬 RGB video naming examples:")
            for file in rgb_videos:
                print(f"  {file}")
        
        if os.path.exists(depth_videos_dir):
            depth_videos = [f for f in os.listdir(depth_videos_dir) if f.endswith('.mp4')]
            print(f"\n🎬 Depth video naming examples:")
            for file in depth_videos:
                print(f"  {file}")
    
    print("\n" + "=" * 60)
    print("🎉 Image and depth extraction test completed!")
    
    print("\nGenerated directories:")
    for test_dir in ["test_extracted_single", "test_extracted_dataset"]:
        if os.path.exists(test_dir):
            print(f"  📁 {test_dir}/")
            subdirs = [d for d in os.listdir(test_dir) if os.path.isdir(os.path.join(test_dir, d))]
            for subdir in subdirs:
                subdir_path = os.path.join(test_dir, subdir)
                file_count = len([f for f in os.listdir(subdir_path) if os.path.isfile(os.path.join(subdir_path, f))])
                print(f"    📁 {subdir}/: {file_count} files")

if __name__ == "__main__":
    test_image_depth_extraction()
