# PKL 图像提取和视频生成工具

## 概述

修改后的 `pkl2.py` 脚本现在支持：
1. 从单个 pkl 文件中提取图像
2. 从目录中的多个 pkl 文件按时间顺序生成 MP4 视频

## 功能特性

- **时间戳解析**: 自动解析文件名中的时间戳（支持格式：`2025-06-09T16_51_05.803324.pkl`）
- **图像解码**: 支持多种图像格式（压缩图像、原始数据等）
- **多摄像头支持**: 自动检测并处理多个摄像头的图像
- **网格布局**: 将多个摄像头的图像排列成网格
- **错误处理**: 跳过损坏的 pkl 文件，继续处理有效文件

## 使用方法

### 1. 单个文件处理

```bash
python data_scape/pkl2.py <path_to_pkl_file>
```

示例：
```bash
python data_scape/pkl2.py /home/<USER>/data/episode_demo/gpt_franka2_pick_up_place_apple/0609_165039/2025-06-09T16_50_53.997363.pkl
```

功能：
- 显示 pkl 文件内容信息
- 提取并显示图像信息
- 保存第一个摄像头的预览图像

### 2. 分别生成每个摄像头的视频（推荐）

```bash
python data_scape/pkl2.py <directory_path> [output_dir] [fps]
```

参数说明：
- `directory_path`: 包含 pkl 文件的目录路径
- `output_dir`: 输出视频的目录（可选，默认：当前目录）
- `fps`: 视频帧率（可选，默认：10）

示例：
```bash
# 在当前目录生成视频
python data_scape/pkl2.py /path/to/pkl_directory

# 在指定目录生成视频，自定义帧率
python data_scape/pkl2.py /path/to/pkl_directory ./videos 15
```

输出：为每个摄像头生成单独的视频文件，如：
- `front.mp4`
- `left.mp4`
- `right.mp4`
- `wrist.mp4`

### 3. 生成网格布局视频（传统模式）

```bash
python data_scape/pkl2.py <directory_path> --grid [output_video.mp4] [fps] [grid_cols] [grid_rows]
```

参数说明：
- `directory_path`: 包含 pkl 文件的目录路径
- `--grid`: 启用网格模式的标志
- `output_video.mp4`: 输出视频文件名（可选，默认：output_video.mp4）
- `fps`: 视频帧率（可选，默认：10）
- `grid_cols`: 网格列数（可选，默认：2）
- `grid_rows`: 网格行数（可选，默认：2）

示例：
```bash
# 生成网格视频
python data_scape/pkl2.py /path/to/pkl_directory --grid combined.mp4 15 2 2
```

## 输出示例

### 单文件处理输出：
```
Processing single pkl file: /path/to/file.pkl
Successfully loaded data of type: <class 'dict'>
Found 4 images:
  front: (480, 640, 3)
  left: (480, 640, 3)
  right: (480, 640, 3)
  wrist: (480, 640, 3)
Saved preview image: /path/to/file_preview.jpg
```

### 分别生成摄像头视频输出：
```
Processing directory for separate camera videos: /path/to/pkl_directory
Output directory: ./videos
FPS: 15
Scanning directory: /path/to/pkl_directory
Found 431 pkl files
Detecting camera views...
Found camera views: ['left', 'front', 'wrist', 'right']
Processing 1/431: 2025-06-09T16_50_53.997363.pkl
  Initialized video writer for front: 640x480 at 15 fps
  Initialized video writer for left: 640x480 at 15 fps
  Initialized video writer for right: 640x480 at 15 fps
  Initialized video writer for wrist: 640x480 at 15 fps
...
Successfully created video for front with 105 frames
Successfully created video for left with 105 frames
Successfully created video for right with 105 frames
Successfully created video for wrist with 105 frames

✓ Successfully created videos for: front, left, right, wrist
Video saved to: ./videos/front.mp4
Video saved to: ./videos/left.mp4
Video saved to: ./videos/right.mp4
Video saved to: ./videos/wrist.mp4
```

### 网格视频生成输出：
```
Processing directory for grid video: /path/to/pkl_directory
Output video: combined.mp4
FPS: 15
Grid size: 2x2
Scanning directory: /path/to/pkl_directory
Found 431 pkl files
Processing 1/431: 2025-06-09T16_50_53.997363.pkl
Initialized video writer: 1280x1020 at 15 fps
...
Successfully created video with 105 frames
Video saved to: combined.mp4
```

## 支持的图像格式

脚本自动检测和处理以下图像格式：
- 压缩图像（JPEG、PNG 等）
- 原始像素数据
- 常见分辨率：720x1280x3、480x640x3、480x640x4

## 错误处理

- 自动跳过损坏或无效的 pkl 文件
- 显示详细的错误信息
- 继续处理其他有效文件

## 依赖项

确保安装以下 Python 包：
```bash
pip install opencv-python numpy
```

## 注意事项

1. 文件名必须包含时间戳格式：`YYYY-MM-DDTHH_MM_SS.ffffff.pkl`
2. pkl 文件中的图像数据应存储在 `data['images']` 字典中
3. 生成的视频使用 MP4 格式，编码器为 'mp4v'
4. 网格大小会影响最终视频的分辨率

## 测试结果

在测试中，脚本成功处理了 431 个 pkl 文件中的 105 个有效文件：

### 分别生成摄像头视频：
- `front.mp4`: 820KB (105 帧)
- `left.mp4`: 1.0MB (105 帧)
- `right.mp4`: 456KB (105 帧)
- `wrist.mp4`: 424KB (105 帧)

### 网格视频：
- `combined.mp4`: 2.5MB (105 帧，2x2 网格布局)

## 优势

### 分别生成摄像头视频的优势：
1. **文件大小更小**：每个视频只包含单个摄像头的内容
2. **便于分析**：可以单独查看每个视角的内容
3. **灵活性高**：可以选择性地使用某些视角的视频
4. **处理效率**：并行处理多个摄像头，提高效率

### 网格视频的优势：
1. **同步查看**：可以同时观看所有摄像头视角
2. **对比分析**：便于比较不同视角的内容
3. **单文件管理**：只需要管理一个视频文件
