#!/usr/bin/env python3
"""
Demo script for dataset processing with pkl2.py
"""

import os
import subprocess
import sys

def run_dataset_demo():
    """Run demonstration of dataset processing functionality"""
    
    print("🎬 PKL Dataset Processing Demo")
    print("=" * 60)
    
    # New dataset directory
    dataset_dir = "/home/<USER>/data/episode_demo/gpt_franka_fr3_dualArm-gripper-6cameras_4_transfer_cubes_with_obstacle_250603-2"
    
    if not os.path.exists(dataset_dir):
        print(f"❌ Dataset directory not found: {dataset_dir}")
        print("Please check the dataset path.")
        return
    
    print(f"📁 Using dataset directory: {dataset_dir}")
    print()
    
    # Demo 1: Single sample processing
    print("🔍 Demo 1: Single sample processing")
    print("-" * 50)
    
    # Find a sample directory
    sample_dirs = [d for d in os.listdir(dataset_dir) 
                   if os.path.isdir(os.path.join(dataset_dir, d))]
    
    if sample_dirs:
        sample_dir = os.path.join(dataset_dir, sample_dirs[0])
        print(f"Processing sample: {sample_dirs[0]}")
        
        # Create output directory for single sample
        single_output_dir = "demo_single_sample"
        if not os.path.exists(single_output_dir):
            os.makedirs(single_output_dir)
        
        result = subprocess.run([
            sys.executable, "data_scape/pkl2.py", 
            sample_dir, single_output_dir, "15"
        ], capture_output=True, text=True)
        
        print("Output:")
        print(result.stdout)
        if result.stderr:
            print("Errors:", result.stderr)
        
        # Show generated files
        if os.path.exists(single_output_dir):
            video_files = [f for f in os.listdir(single_output_dir) if f.endswith('.mp4')]
            if video_files:
                print(f"\n✅ Generated {len(video_files)} videos for single sample:")
                for video_file in sorted(video_files):
                    file_path = os.path.join(single_output_dir, video_file)
                    file_size = os.path.getsize(file_path)
                    print(f"  📹 {video_file}: {file_size:,} bytes")
    
    print("\n" + "=" * 60)
    
    # Demo 2: Dataset processing (all samples)
    print("🎥 Demo 2: Dataset processing (ALL SAMPLES)")
    print("-" * 60)
    
    dataset_output_dir = "demo_dataset_videos"
    print(f"Processing entire dataset...")
    print(f"Output directory: {dataset_output_dir}")
    
    result = subprocess.run([
        sys.executable, "data_scape/pkl2.py", 
        dataset_dir, "--dataset", dataset_output_dir, "15"
    ], capture_output=True, text=True)
    
    print("Output:")
    print(result.stdout)
    if result.stderr:
        print("Errors:", result.stderr)
    
    # Show generated files
    if os.path.exists(dataset_output_dir):
        sample_dirs = [d for d in os.listdir(dataset_output_dir) 
                      if os.path.isdir(os.path.join(dataset_output_dir, d))]
        
        if sample_dirs:
            print(f"\n✅ Generated videos for {len(sample_dirs)} samples:")
            total_videos = 0
            total_size = 0
            
            for sample_name in sorted(sample_dirs):
                sample_path = os.path.join(dataset_output_dir, sample_name)
                video_files = [f for f in os.listdir(sample_path) if f.endswith('.mp4')]
                
                if video_files:
                    sample_size = sum(os.path.getsize(os.path.join(sample_path, f)) 
                                    for f in video_files)
                    total_videos += len(video_files)
                    total_size += sample_size
                    
                    print(f"  📁 {sample_name}: {len(video_files)} videos ({sample_size:,} bytes)")
                    for video_file in sorted(video_files):
                        file_path = os.path.join(sample_path, video_file)
                        file_size = os.path.getsize(file_path)
                        print(f"    📹 {video_file}: {file_size:,} bytes")
            
            print(f"\n📊 Total: {total_videos} videos, {total_size:,} bytes")
    
    print("\n" + "=" * 60)
    
    # Demo 3: Single file processing
    print("🔍 Demo 3: Single file processing")
    print("-" * 40)
    
    if sample_dirs:
        sample_dir = os.path.join(dataset_dir, sample_dirs[0])
        pkl_files = [f for f in os.listdir(sample_dir) if f.endswith('.pkl')]
        
        if pkl_files:
            sample_file = os.path.join(sample_dir, pkl_files[0])
            print(f"Processing: {sample_dirs[0]}/{pkl_files[0]}")
            
            result = subprocess.run([
                sys.executable, "data_scape/pkl2.py", sample_file
            ], capture_output=True, text=True)
            
            print("Output:")
            print(result.stdout)
            if result.stderr:
                print("Errors:", result.stderr)
    
    print("\n" + "=" * 60)
    print("🎉 Dataset processing demo completed!")
    
    print("\nGenerated directories:")
    for demo_dir in ["demo_single_sample", "demo_dataset_videos"]:
        if os.path.exists(demo_dir):
            print(f"  📁 {demo_dir}/")
    
    print(f"\n📖 For more information, see: PKL_TO_VIDEO_README.md")

if __name__ == "__main__":
    run_dataset_demo()
