#!/usr/bin/env python3
"""
Test script for parent directory output functionality
"""

import os
import subprocess
import sys

def test_parent_directory_output():
    """Test the parent directory output functionality"""
    
    print("📁 Parent Directory Output Test")
    print("=" * 60)
    
    # Test dataset directory
    dataset_dir = "/home/<USER>/data/episode_demo/gpt_franka_fr3_dualArm-gripper-6cameras_4_transfer_cubes_with_obstacle_250603-2"
    
    if not os.path.exists(dataset_dir):
        print(f"❌ Dataset directory not found: {dataset_dir}")
        return
    
    # Find a sample directory
    sample_dirs = [d for d in os.listdir(dataset_dir) 
                   if os.path.isdir(os.path.join(dataset_dir, d))]
    
    if not sample_dirs:
        print("❌ No sample directories found")
        return
    
    sample_dir = os.path.join(dataset_dir, sample_dirs[0])
    print(f"📁 Using sample directory: {sample_dirs[0]}")
    print(f"📁 Full sample path: {sample_dir}")
    print(f"📁 Expected parent output: {dataset_dir}")
    print()
    
    # Test 1: Single sample extraction to parent directory
    print("🔍 Test 1: Single sample extraction to parent directory")
    print("-" * 60)
    
    print(f"Extracting to parent directory (auto)")
    
    result = subprocess.run([
        sys.executable, "data_scape/pkl2.py", 
        sample_dir, "--extract", "auto", "false"  # Don't save individual images for speed
    ], capture_output=True, text=True)
    
    print("Output:")
    print(result.stdout)
    if result.stderr:
        print("Errors:", result.stderr)
    
    # Check if files were created in parent directory
    expected_dirs = ["rgb_images", "depth_images", "rgb_videos", "depth_videos"]
    
    print(f"\n✅ Checking output in parent directory: {dataset_dir}")
    
    for dir_name in expected_dirs:
        dir_path = os.path.join(dataset_dir, dir_name)
        if os.path.exists(dir_path):
            file_count = len([f for f in os.listdir(dir_path) if os.path.isfile(os.path.join(dir_path, f))])
            print(f"  📁 {dir_name}/: {file_count} files")
            
            # Show some example files
            files = [f for f in os.listdir(dir_path) if os.path.isfile(os.path.join(dir_path, f))][:3]
            for file in files:
                file_path = os.path.join(dir_path, file)
                file_size = os.path.getsize(file_path)
                print(f"    📄 {file}: {file_size:,} bytes")
        else:
            print(f"  ❌ {dir_name}/: Not found")
    
    print("\n" + "=" * 60)
    
    # Test 2: Check directory structure
    print("🔍 Test 2: Directory structure verification")
    print("-" * 60)
    
    print(f"Dataset directory structure:")
    print(f"📁 {os.path.basename(dataset_dir)}/")
    
    # List all items in dataset directory
    items = os.listdir(dataset_dir)
    items.sort()
    
    for item in items:
        item_path = os.path.join(dataset_dir, item)
        if os.path.isdir(item_path):
            if item in expected_dirs:
                file_count = len([f for f in os.listdir(item_path) if os.path.isfile(os.path.join(item_path, f))])
                print(f"  📁 {item}/: {file_count} files (🎬 Generated)")
            else:
                # Check if it's a sample directory
                pkl_files = [f for f in os.listdir(item_path) if f.endswith('.pkl')]
                if pkl_files:
                    print(f"  📁 {item}/: {len(pkl_files)} pkl files (📊 Sample)")
                else:
                    print(f"  📁 {item}/: directory")
        else:
            file_size = os.path.getsize(item_path)
            print(f"  📄 {item}: {file_size:,} bytes")
    
    print("\n" + "=" * 60)
    
    # Test 3: Test with specific output directory (for comparison)
    print("🔍 Test 3: Comparison with specific output directory")
    print("-" * 60)
    
    specific_output = "test_specific_output"
    print(f"Extracting to specific directory: {specific_output}")
    
    result = subprocess.run([
        sys.executable, "data_scape/pkl2.py", 
        sample_dir, "--extract", specific_output, "false"
    ], capture_output=True, text=True)
    
    print("Output:")
    print(result.stdout)
    
    # Check specific output directory
    if os.path.exists(specific_output):
        print(f"\n✅ Specific output directory created: {specific_output}")
        
        for dir_name in expected_dirs:
            dir_path = os.path.join(specific_output, dir_name)
            if os.path.exists(dir_path):
                file_count = len([f for f in os.listdir(dir_path) if os.path.isfile(os.path.join(dir_path, f))])
                print(f"  📁 {dir_name}/: {file_count} files")
            else:
                print(f"  ❌ {dir_name}/: Not found")
    
    print("\n" + "=" * 60)
    
    # Test 4: Path analysis
    print("🔍 Test 4: Path analysis")
    print("-" * 60)
    
    print("Path analysis:")
    print(f"  Sample directory: {sample_dir}")
    print(f"  Parent directory: {os.path.dirname(sample_dir)}")
    print(f"  Absolute parent: {os.path.dirname(os.path.abspath(sample_dir))}")
    print(f"  Dataset directory: {dataset_dir}")
    print(f"  Match: {os.path.dirname(os.path.abspath(sample_dir)) == os.path.abspath(dataset_dir)}")
    
    print("\n" + "=" * 60)
    print("🎉 Parent directory output test completed!")
    
    print("\nKey advantages of parent directory output:")
    print("✅ Files are organized alongside the original data")
    print("✅ Easy to find extracted content relative to source")
    print("✅ No need to specify output paths manually")
    print("✅ Consistent organization across samples")
    
    print("\nGenerated structure:")
    print("dataset/")
    print("├── sample1/           # Original pkl files")
    print("├── sample2/           # Original pkl files")
    print("├── sample3/           # Original pkl files")
    print("├── rgb_images/        # 🎬 Generated RGB images")
    print("├── depth_images/      # 🎬 Generated depth images")
    print("├── rgb_videos/        # 🎬 Generated RGB videos")
    print("└── depth_videos/      # 🎬 Generated depth videos")
    
    print("\nUsage examples:")
    print("# Extract to parent directory (recommended)")
    print("python pkl2.py /path/to/sample --extract auto true")
    print("")
    print("# Extract to specific directory")
    print("python pkl2.py /path/to/sample --extract ./custom_output true")

if __name__ == "__main__":
    test_parent_directory_output()
