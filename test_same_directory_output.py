#!/usr/bin/env python3
"""
Test script for same directory output functionality
"""

import os
import subprocess
import sys

def test_same_directory_output():
    """Test the same directory output functionality"""
    
    print("📁 Same Directory Output Test")
    print("=" * 60)
    
    # Test dataset directory
    dataset_dir = "/home/<USER>/data/episode_demo/gpt_franka_fr3_dualArm-gripper-6cameras_4_transfer_cubes_with_obstacle_250603-2"
    
    if not os.path.exists(dataset_dir):
        print(f"❌ Dataset directory not found: {dataset_dir}")
        return
    
    # Find a sample directory (exclude generated directories)
    sample_dirs = [d for d in os.listdir(dataset_dir)
                   if os.path.isdir(os.path.join(dataset_dir, d)) and
                   d not in ['rgb_images', 'depth_images', 'rgb_videos', 'depth_videos'] and
                   d.startswith('0606_')]  # Look for actual sample directories
    
    if not sample_dirs:
        print("❌ No sample directories found")
        return
    
    sample_dir = os.path.join(dataset_dir, sample_dirs[0])
    print(f"📁 Using sample directory: {sample_dirs[0]}")
    print(f"📁 Full sample path: {sample_dir}")
    print(f"📁 Expected output: same directory as pkl files")
    print()
    
    # Test 1: Single sample extraction to same directory with images
    print("🔍 Test 1: Single sample extraction to same directory (with PNG images)")
    print("-" * 60)
    
    print(f"Extracting to same directory with PNG images")
    
    result = subprocess.run([
        sys.executable, "data_scape/pkl2.py", 
        sample_dir, "--extract", "auto", "true"  # Save individual images
    ], capture_output=True, text=True)
    
    print("Output:")
    print(result.stdout)
    if result.stderr:
        print("Errors:", result.stderr)
    
    # Check if files were created in same directory
    expected_dirs = ["rgb_images", "depth_images", "rgb_videos", "depth_videos"]
    
    print(f"\n✅ Checking output in same directory: {sample_dir}")
    
    for dir_name in expected_dirs:
        dir_path = os.path.join(sample_dir, dir_name)
        if os.path.exists(dir_path):
            file_count = len([f for f in os.listdir(dir_path) if os.path.isfile(os.path.join(dir_path, f))])
            print(f"  📁 {dir_name}/: {file_count} files")
            
            # Show some example files
            files = [f for f in os.listdir(dir_path) if os.path.isfile(os.path.join(dir_path, f))][:3]
            for file in files:
                file_path = os.path.join(dir_path, file)
                file_size = os.path.getsize(file_path)
                print(f"    📄 {file}: {file_size:,} bytes")
        else:
            print(f"  ❌ {dir_name}/: Not found")
    
    print("\n" + "=" * 60)
    
    # Test 2: Check directory structure
    print("🔍 Test 2: Sample directory structure verification")
    print("-" * 60)
    
    print(f"Sample directory structure:")
    print(f"📁 {os.path.basename(sample_dir)}/")
    
    # List all items in sample directory
    items = os.listdir(sample_dir)
    items.sort()
    
    pkl_count = 0
    for item in items:
        item_path = os.path.join(sample_dir, item)
        if os.path.isdir(item_path):
            if item in expected_dirs:
                file_count = len([f for f in os.listdir(item_path) if os.path.isfile(os.path.join(item_path, f))])
                print(f"  📁 {item}/: {file_count} files (🎬 Generated)")
            else:
                print(f"  📁 {item}/: directory")
        else:
            if item.endswith('.pkl'):
                pkl_count += 1
            else:
                file_size = os.path.getsize(item_path)
                print(f"  📄 {item}: {file_size:,} bytes")
    
    print(f"  📊 Total pkl files: {pkl_count}")
    
    print("\n" + "=" * 60)
    
    # Test 3: Verify PNG images are saved
    print("🔍 Test 3: PNG images verification")
    print("-" * 60)
    
    rgb_images_dir = os.path.join(sample_dir, "rgb_images")
    depth_images_dir = os.path.join(sample_dir, "depth_images")
    
    if os.path.exists(rgb_images_dir):
        rgb_files = [f for f in os.listdir(rgb_images_dir) if f.endswith('.png')]
        print(f"📷 RGB PNG images: {len(rgb_files)} files")
        
        # Show examples by camera
        cameras = set()
        for rgb_file in rgb_files[:10]:
            parts = rgb_file.split('_')
            if len(parts) >= 3:
                camera = parts[-2]  # Extract camera name
                cameras.add(camera)
        
        print(f"📷 RGB cameras: {sorted(cameras)}")
        
        # Show file examples
        for camera in sorted(cameras):
            camera_files = [f for f in rgb_files if f'_{camera}_rgb.png' in f]
            if camera_files:
                sample_file = camera_files[0]
                file_path = os.path.join(rgb_images_dir, sample_file)
                file_size = os.path.getsize(file_path)
                print(f"   {camera}: {sample_file} ({file_size:,} bytes)")
    
    if os.path.exists(depth_images_dir):
        depth_files = [f for f in os.listdir(depth_images_dir) if f.endswith('.png')]
        print(f"\n🔍 Depth PNG images: {len(depth_files)} files")
        
        # Show examples by camera
        cameras = set()
        for depth_file in depth_files[:10]:
            parts = depth_file.split('_')
            if len(parts) >= 3:
                camera = parts[-2]  # Extract camera name
                cameras.add(camera)
        
        print(f"🔍 Depth cameras: {sorted(cameras)}")
        
        # Show file examples
        for camera in sorted(cameras):
            camera_files = [f for f in depth_files if f'_{camera}_d.png' in f]
            if camera_files:
                sample_file = camera_files[0]
                file_path = os.path.join(depth_images_dir, sample_file)
                file_size = os.path.getsize(file_path)
                print(f"   {camera}: {sample_file} ({file_size:,} bytes)")
    
    print("\n" + "=" * 60)
    
    # Test 4: File naming convention verification
    print("🔍 Test 4: File naming convention verification")
    print("-" * 60)
    
    if os.path.exists(rgb_images_dir):
        rgb_files = [f for f in os.listdir(rgb_images_dir) if f.endswith('.png')][:5]
        print("📷 RGB image naming examples:")
        for file in rgb_files:
            print(f"  {file}")
    
    if os.path.exists(depth_images_dir):
        depth_files = [f for f in os.listdir(depth_images_dir) if f.endswith('.png')][:5]
        print("\n🔍 Depth image naming examples:")
        for file in depth_files:
            print(f"  {file}")
    
    rgb_videos_dir = os.path.join(sample_dir, "rgb_videos")
    depth_videos_dir = os.path.join(sample_dir, "depth_videos")
    
    if os.path.exists(rgb_videos_dir):
        rgb_videos = [f for f in os.listdir(rgb_videos_dir) if f.endswith('.mp4')]
        print(f"\n🎬 RGB video naming examples:")
        for file in rgb_videos:
            print(f"  {file}")
    
    if os.path.exists(depth_videos_dir):
        depth_videos = [f for f in os.listdir(depth_videos_dir) if f.endswith('.mp4')]
        print(f"\n🎬 Depth video naming examples:")
        for file in depth_videos:
            print(f"  {file}")
    
    print("\n" + "=" * 60)
    print("🎉 Same directory output test completed!")
    
    print("\nKey advantages of same directory output:")
    print("✅ All related files (pkl, images, videos) in one place")
    print("✅ Easy to find and manage per-sample data")
    print("✅ Clear organization by sample")
    print("✅ PNG images saved for detailed analysis")
    
    print("\nGenerated structure:")
    print("sample_directory/")
    print("├── *.pkl              # Original pkl files")
    print("├── rgb_images/        # 🎬 Generated RGB PNG images")
    print("├── depth_images/      # 🎬 Generated depth PNG images")
    print("├── rgb_videos/        # 🎬 Generated RGB videos")
    print("└── depth_videos/      # 🎬 Generated depth videos")
    
    print("\nUsage examples:")
    print("# Extract to same directory with PNG images (recommended)")
    print("python pkl2.py /path/to/sample --extract auto true")
    print("")
    print("# Extract to same directory without PNG images")
    print("python pkl2.py /path/to/sample --extract auto false")

if __name__ == "__main__":
    test_same_directory_output()
